import 'package:intl/intl.dart';

class AppUtils {
  // Date formatting
  static String formatDate(DateTime date) {
    return DateFormat('dd MMMM yyyy', 'id_ID').format(date);
  }
  
  static String formatTime(DateTime time) {
    return DateFormat('HH:mm').format(time);
  }
  
  static String formatDateShort(DateTime date) {
    return DateFormat('dd/MM/yyyy').format(date);
  }
  
  // Islamic date helpers
  static bool isToday(DateTime date) {
    final now = DateTime.now();
    return date.year == now.year && 
           date.month == now.month && 
           date.day == now.day;
  }
  
  static DateTime getDateOnly(DateTime dateTime) {
    return DateTime(dateTime.year, dateTime.month, dateTime.day);
  }
  
  // Prayer time helpers
  static String getPrayerTimeDisplayName(String prayerName) {
    switch (prayerName.toLowerCase()) {
      case 'fajr':
        return 'Subuh';
      case 'sunrise':
        return 'Terbit';
      case 'dhuhr':
        return 'D<PERSON>hur';
      case 'asr':
        return 'Ashar';
      case 'maghrib':
        return 'Maghrib';
      case 'isha':
        return 'Isya';
      default:
        return prayerName;
    }
  }
  
  // Dzikir type helpers
  static String getDzikirDisplayName(String dzikirType) {
    switch (dzikirType) {
      case 'pagi':
        return 'Dzikir Pagi';
      case 'petang':
        return 'Dzikir Petang';
      case 'sesudah_sholat':
        return 'Dzikir Sesudah Sholat';
      default:
        return dzikirType;
    }
  }
  
  static String getDzikirDescription(String dzikirType) {
    switch (dzikirType) {
      case 'pagi':
        return 'Setelah Subuh hingga sebelum Dzuhur';
      case 'petang':
        return 'Setelah Ashar hingga sebelum Isya';
      case 'sesudah_sholat':
        return 'Setelah sholat fardhu';
      default:
        return '';
    }
  }
  
  // Progress helpers
  static String getProgressText(int current, int target) {
    return '$current / $target';
  }
  
  static String getPercentageText(double percentage) {
    return '${(percentage * 100).toInt()}%';
  }
  
  // Streak helpers
  static String getStreakText(int days) {
    if (days == 0) return 'Belum ada streak';
    if (days == 1) return '1 hari berturut-turut';
    return '$days hari berturut-turut';
  }
  
  // Validation helpers
  static bool isValidEmail(String email) {
    return RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email);
  }
  
  static bool isValidTime(int hour, int minute) {
    return hour >= 0 && hour <= 23 && minute >= 0 && minute <= 59;
  }
}
