import 'package:isar/isar.dart';
import 'package:path_provider/path_provider.dart';
import '../models/dzikir_item.dart';
import '../models/user_progress.dart';

class DatabaseService {
  static DatabaseService? _instance;
  static Isar? _isar;

  DatabaseService._();

  static DatabaseService get instance {
    _instance ??= DatabaseService._();
    return _instance!;
  }

  static Future<Isar> get isar async {
    if (_isar != null) return _isar!;
    
    final dir = await getApplicationDocumentsDirectory();
    _isar = await Isar.open(
      [DzikirItemSchema, UserProgressSchema, DailyStreakSchema],
      directory: dir.path,
      name: 'dzikir_db',
    );
    
    return _isar!;
  }

  // Initialize database and load initial data
  static Future<void> initialize() async {
    final db = await isar;
    
    // Check if data already exists
    final count = await db.dzikirItems.count();
    if (count == 0) {
      await _loadInitialData();
    }
  }

  // Load initial dzikir data from JSON files
  static Future<void> _loadInitialData() async {
    final db = await isar;
    
    // This will be implemented to load from JSON assets
    // For now, we'll add some sample data
    await db.writeTxn(() async {
      // Sample data will be replaced with JSON loading
      final sampleItem = DzikirItem.create(
        itemId: 'sample_ayat_kursi',
        judul: 'Ayat Kursi',
        arab: 'اللَّهُ لَا إِلَٰهَ إِلَّا هُوَ الْحَيُّ الْقَيُّومُ',
        transliterasi: 'Allahu la ilaha illa huwal hayyul qayyum',
        terjemahan: 'Allah, tidak ada Tuhan selain Dia, Yang Maha Hidup',
        jumlahTarget: 1,
        waktu: 'pagi',
        rujukan: 'QS. Al-Baqarah: 255',
        urutan: 1,
      );
      
      await db.dzikirItems.put(sampleItem);
    });
  }

  // Dzikir Items CRUD operations
  Future<List<DzikirItem>> getAllDzikirItems() async {
    final db = await isar;
    return await db.dzikirItems.where().sortByUrutan().findAll();
  }

  Future<List<DzikirItem>> getDzikirItemsByType(String waktu) async {
    final db = await isar;
    return await db.dzikirItems
        .filter()
        .waktuEqualTo(waktu)
        .sortByUrutan()
        .findAll();
  }

  Future<DzikirItem?> getDzikirItemById(String itemId) async {
    final db = await isar;
    return await db.dzikirItems
        .filter()
        .itemIdEqualTo(itemId)
        .findFirst();
  }

  Future<void> updateDzikirItem(DzikirItem item) async {
    final db = await isar;
    await db.writeTxn(() async {
      await db.dzikirItems.put(item);
    });
  }

  Future<void> resetDzikirProgress(String waktu) async {
    final db = await isar;
    final items = await getDzikirItemsByType(waktu);
    
    await db.writeTxn(() async {
      for (final item in items) {
        item.resetCount();
        await db.dzikirItems.put(item);
      }
    });
  }

  // User Progress operations
  Future<UserProgress?> getTodayProgress(String dzikirType) async {
    final db = await isar;
    final today = DateTime.now();
    final todayDate = DateTime(today.year, today.month, today.day);
    
    return await db.userProgresses
        .filter()
        .dateEqualTo(todayDate)
        .and()
        .dzikirTypeEqualTo(dzikirType)
        .findFirst();
  }

  Future<void> updateProgress(String dzikirType, int completed, int total) async {
    final db = await isar;
    final today = DateTime.now();
    final todayDate = DateTime(today.year, today.month, today.day);
    
    await db.writeTxn(() async {
      var progress = await db.userProgresses
          .filter()
          .dateEqualTo(todayDate)
          .and()
          .dzikirTypeEqualTo(dzikirType)
          .findFirst();
      
      if (progress == null) {
        progress = UserProgress.create(
          date: todayDate,
          dzikirType: dzikirType,
          totalItems: total,
        );
      }
      
      progress.updateProgress(completed);
      await db.userProgresses.put(progress);
    });
  }

  // Daily Streak operations
  Future<DailyStreak?> getTodayStreak() async {
    final db = await isar;
    final today = DateTime.now();
    final todayDate = DateTime(today.year, today.month, today.day);
    
    return await db.dailyStreaks
        .filter()
        .dateEqualTo(todayDate)
        .findFirst();
  }

  Future<void> updateDailyStreak(String dzikirType, bool completed) async {
    final db = await isar;
    final today = DateTime.now();
    final todayDate = DateTime(today.year, today.month, today.day);
    
    await db.writeTxn(() async {
      var streak = await db.dailyStreaks
          .filter()
          .dateEqualTo(todayDate)
          .findFirst();
      
      if (streak == null) {
        streak = DailyStreak.create(date: todayDate);
      }
      
      switch (dzikirType) {
        case 'pagi':
          streak.morningCompleted = completed;
          break;
        case 'petang':
          streak.eveningCompleted = completed;
          break;
        case 'sesudah_sholat':
          streak.afterPrayerCompleted = completed;
          break;
      }
      
      await db.dailyStreaks.put(streak);
    });
  }

  Future<int> getCurrentStreak() async {
    final db = await isar;
    final streaks = await db.dailyStreaks
        .where()
        .sortByDateDesc()
        .limit(100)
        .findAll();
    
    int currentStreak = 0;
    final today = DateTime.now();
    
    for (final streak in streaks) {
      final daysDiff = today.difference(streak.date).inDays;
      
      if (daysDiff == currentStreak && streak.isFullyCompleted) {
        currentStreak++;
      } else {
        break;
      }
    }
    
    return currentStreak;
  }

  // Cleanup old data
  Future<void> cleanupOldData() async {
    final db = await isar;
    final cutoffDate = DateTime.now().subtract(const Duration(days: 90));
    
    await db.writeTxn(() async {
      await db.userProgresses
          .filter()
          .dateLessThan(cutoffDate)
          .deleteAll();
      
      await db.dailyStreaks
          .filter()
          .dateLessThan(cutoffDate)
          .deleteAll();
    });
  }

  // Close database
  Future<void> close() async {
    if (_isar != null) {
      await _isar!.close();
      _isar = null;
    }
  }
}
