import 'dart:convert';
import 'package:flutter/services.dart';
import '../models/dzikir_item.dart';
import '../models/user_progress.dart';
import 'database_service.dart';

class DzikirRepository {
  static DzikirRepository? _instance;
  final DatabaseService _databaseService = DatabaseService.instance;

  DzikirRepository._();

  static DzikirRepository get instance {
    _instance ??= DzikirRepository._();
    return _instance!;
  }

  // Initialize repository and load data from JSON assets
  Future<void> initialize() async {
    await DatabaseService.initialize();
    await _loadDzikirDataFromAssets();
  }

  // Load dzikir data from JSON assets
  Future<void> _loadDzikirDataFromAssets() async {
    try {
      // Check if data already exists
      final existingItems = await _databaseService.getAllDzikirItems();
      if (existingItems.isNotEmpty) return;

      // Load morning dzikir
      await _loadDzikirFromAsset('assets/data/dzikir_pagi.json');
      
      // Load evening dzikir
      await _loadDzikirFromAsset('assets/data/dzikir_petang.json');
      
      // Load after prayer dzikir
      await _loadDzikirFromAsset('assets/data/dzikir_sesudah_sholat.json');
      
    } catch (e) {
      print('Error loading dzikir data: $e');
    }
  }

  Future<void> _loadDzikirFromAsset(String assetPath) async {
    try {
      final jsonString = await rootBundle.loadString(assetPath);
      final List<dynamic> jsonList = json.decode(jsonString);
      
      final db = await DatabaseService.isar;
      await db.writeTxn(() async {
        for (final jsonItem in jsonList) {
          final dzikirItem = DzikirItem.fromJson(jsonItem);
          await db.dzikirItems.put(dzikirItem);
        }
      });
    } catch (e) {
      print('Error loading from $assetPath: $e');
    }
  }

  // Get dzikir items by type
  Future<List<DzikirItem>> getDzikirItems(String waktu) async {
    return await _databaseService.getDzikirItemsByType(waktu);
  }

  // Get all dzikir items
  Future<List<DzikirItem>> getAllDzikirItems() async {
    return await _databaseService.getAllDzikirItems();
  }

  // Get specific dzikir item
  Future<DzikirItem?> getDzikirItem(String itemId) async {
    return await _databaseService.getDzikirItemById(itemId);
  }

  // Update dzikir item progress
  Future<void> incrementDzikirCount(String itemId) async {
    final item = await _databaseService.getDzikirItemById(itemId);
    if (item != null) {
      item.incrementCount();
      await _databaseService.updateDzikirItem(item);
      
      // Update overall progress
      await _updateOverallProgress(item.waktu);
    }
  }

  // Reset dzikir progress for a specific type
  Future<void> resetDzikirProgress(String waktu) async {
    await _databaseService.resetDzikirProgress(waktu);
    await _updateOverallProgress(waktu);
  }

  // Mark dzikir as completed
  Future<void> markDzikirCompleted(String itemId) async {
    final item = await _databaseService.getDzikirItemById(itemId);
    if (item != null) {
      item.markAsCompleted();
      await _databaseService.updateDzikirItem(item);
      await _updateOverallProgress(item.waktu);
    }
  }

  // Get progress for today
  Future<UserProgress?> getTodayProgress(String dzikirType) async {
    return await _databaseService.getTodayProgress(dzikirType);
  }

  // Get completion percentage for a dzikir type
  Future<double> getCompletionPercentage(String waktu) async {
    final items = await getDzikirItems(waktu);
    if (items.isEmpty) return 0.0;
    
    final completedItems = items.where((item) => item.isCompleted).length;
    return completedItems / items.length;
  }

  // Get total progress count for a dzikir type
  Future<Map<String, int>> getProgressCount(String waktu) async {
    final items = await getDzikirItems(waktu);
    int completed = 0;
    int total = items.length;
    
    for (final item in items) {
      if (item.isCompleted) completed++;
    }
    
    return {'completed': completed, 'total': total};
  }

  // Check if dzikir type is fully completed today
  Future<bool> isDzikirCompleted(String waktu) async {
    final items = await getDzikirItems(waktu);
    return items.every((item) => item.isCompleted);
  }

  // Update overall progress for a dzikir type
  Future<void> _updateOverallProgress(String waktu) async {
    final progressCount = await getProgressCount(waktu);
    await _databaseService.updateProgress(
      waktu,
      progressCount['completed']!,
      progressCount['total']!,
    );
    
    // Update daily streak
    final isCompleted = await isDzikirCompleted(waktu);
    await _databaseService.updateDailyStreak(waktu, isCompleted);
  }

  // Get current streak
  Future<int> getCurrentStreak() async {
    return await _databaseService.getCurrentStreak();
  }

  // Get today's streak info
  Future<DailyStreak?> getTodayStreak() async {
    return await _databaseService.getTodayStreak();
  }

  // Get progress history for the last N days
  Future<List<UserProgress>> getProgressHistory(String dzikirType, int days) async {
    final db = await DatabaseService.isar;
    final endDate = DateTime.now();
    final startDate = endDate.subtract(Duration(days: days));
    
    return await db.userProgresses
        .filter()
        .dzikirTypeEqualTo(dzikirType)
        .and()
        .dateGreaterThan(startDate)
        .sortByDateDesc()
        .findAll();
  }

  // Get weekly progress summary
  Future<Map<String, dynamic>> getWeeklyProgressSummary() async {
    final now = DateTime.now();
    final weekStart = now.subtract(Duration(days: now.weekday - 1));
    
    final morningProgress = await getProgressHistory('pagi', 7);
    final eveningProgress = await getProgressHistory('petang', 7);
    final afterPrayerProgress = await getProgressHistory('sesudah_sholat', 7);
    
    return {
      'week_start': weekStart,
      'morning_completed': morningProgress.where((p) => p.isFullyCompleted).length,
      'evening_completed': eveningProgress.where((p) => p.isFullyCompleted).length,
      'after_prayer_completed': afterPrayerProgress.where((p) => p.isFullyCompleted).length,
      'total_days': 7,
    };
  }

  // Reset all progress (for testing or new day)
  Future<void> resetAllProgress() async {
    await _databaseService.resetDzikirProgress('pagi');
    await _databaseService.resetDzikirProgress('petang');
    await _databaseService.resetDzikirProgress('sesudah_sholat');
  }

  // Cleanup old data
  Future<void> cleanupOldData() async {
    await _databaseService.cleanupOldData();
  }

  // Close repository
  Future<void> close() async {
    await _databaseService.close();
  }
}
