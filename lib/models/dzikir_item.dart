import 'package:isar/isar.dart';

part 'dzikir_item.g.dart';

@collection
class DzikirItem {
  Id id = Isar.autoIncrement;
  
  @Index()
  late String itemId; // Unique identifier like "pagi_ayat_kursi"
  
  late String judul;
  late String arab;
  late String transliterasi;
  late String terjemahan;
  late int jumlahTarget;
  
  @Index()
  late String waktu; // "pagi", "petang", "sesudah_sholat"
  
  late String rujukan;
  late int urutan; // Order in the list
  
  // Progress tracking
  int currentCount = 0;
  DateTime? lastRead;
  bool isCompleted = false;
  
  DzikirItem();
  
  DzikirItem.create({
    required this.itemId,
    required this.judul,
    required this.arab,
    required this.transliterasi,
    required this.terjemahan,
    required this.jumlahTarget,
    required this.waktu,
    required this.rujukan,
    required this.urutan,
  });
  
  // Factory constructor from JSON
  factory DzikirItem.fromJson(Map<String, dynamic> json) {
    return DzikirItem.create(
      itemId: json['id'] as String,
      judul: json['judul'] as String,
      arab: json['arab'] as String,
      transliterasi: json['transliterasi'] as String,
      terjemahan: json['terjemahan'] as String,
      jumlahTarget: json['jumlahTarget'] as int,
      waktu: json['waktu'] as String,
      rujukan: json['rujukan'] as String,
      urutan: json['urutan'] as int? ?? 0,
    );
  }
  
  // Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': itemId,
      'judul': judul,
      'arab': arab,
      'transliterasi': transliterasi,
      'terjemahan': terjemahan,
      'jumlahTarget': jumlahTarget,
      'waktu': waktu,
      'rujukan': rujukan,
      'urutan': urutan,
    };
  }
  
  // Helper methods
  bool get isTargetReached => currentCount >= jumlahTarget;
  
  double get progressPercentage => 
      jumlahTarget > 0 ? (currentCount / jumlahTarget).clamp(0.0, 1.0) : 0.0;
  
  void incrementCount() {
    if (currentCount < jumlahTarget) {
      currentCount++;
      lastRead = DateTime.now();
      isCompleted = isTargetReached;
    }
  }
  
  void resetCount() {
    currentCount = 0;
    isCompleted = false;
  }
  
  void markAsCompleted() {
    currentCount = jumlahTarget;
    isCompleted = true;
    lastRead = DateTime.now();
  }
}
