import 'package:isar/isar.dart';

part 'user_progress.g.dart';

@collection
class UserProgress {
  Id id = Isar.autoIncrement;
  
  @Index()
  late DateTime date; // Date of the progress (YYYY-MM-DD)
  
  @Index()
  late String dzikirType; // "pagi", "petang", "sesudah_sholat"
  
  late int totalItems;
  late int completedItems;
  late bool isFullyCompleted;
  late DateTime createdAt;
  late DateTime? completedAt;
  
  UserProgress();
  
  UserProgress.create({
    required this.date,
    required this.dzikirType,
    required this.totalItems,
    this.completedItems = 0,
    this.isFullyCompleted = false,
  }) {
    createdAt = DateTime.now();
  }
  
  double get completionPercentage => 
      totalItems > 0 ? (completedItems / totalItems).clamp(0.0, 1.0) : 0.0;
  
  void updateProgress(int completed) {
    completedItems = completed;
    isFullyCompleted = completedItems >= totalItems;
    if (isFullyCompleted && completedAt == null) {
      completedAt = DateTime.now();
    }
  }
}

@collection
class DailyStreak {
  Id id = Isar.autoIncrement;
  
  @Index()
  late DateTime date;
  
  late bool morningCompleted;
  late bool eveningCompleted;
  late bool afterPrayerCompleted;
  
  DailyStreak();
  
  DailyStreak.create({
    required this.date,
    this.morningCompleted = false,
    this.eveningCompleted = false,
    this.afterPrayerCompleted = false,
  });
  
  bool get isFullyCompleted => 
      morningCompleted && eveningCompleted && afterPrayerCompleted;
  
  int get completedCount => 
      (morningCompleted ? 1 : 0) + 
      (eveningCompleted ? 1 : 0) + 
      (afterPrayerCompleted ? 1 : 0);
}
