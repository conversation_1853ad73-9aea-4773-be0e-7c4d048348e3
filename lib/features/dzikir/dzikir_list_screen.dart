import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../core/theme/app_theme.dart';
import '../../core/utils/app_utils.dart';
import '../../shared/widgets/custom_button.dart';
import 'dzikir_detail_screen.dart';

class DzikirListScreen extends ConsumerStatefulWidget {
  final String dzikirType;

  const DzikirListScreen({
    super.key,
    required this.dzikirType,
  });

  @override
  ConsumerState<DzikirListScreen> createState() => _DzikirListScreenState();
}

class _DzikirListScreenState extends ConsumerState<DzikirListScreen> {
  @override
  Widget build(BuildContext context) {
    final displayName = AppUtils.getDzikirDisplayName(widget.dzikirType);
    final description = AppUtils.getDzikirDescription(widget.dzikirType);
    
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      body: CustomScrollView(
        slivers: [
          // App Bar
          SliverAppBar(
            expandedHeight: 160,
            floating: false,
            pinned: true,
            backgroundColor: _getThemeColor(),
            flexibleSpace: FlexibleSpaceBar(
              title: Text(
                displayName,
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
              background: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      _getThemeColor(),
                      _getThemeColor().withOpacity(0.8),
                    ],
                  ),
                ),
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const SizedBox(height: 40),
                      Icon(
                        _getThemeIcon(),
                        size: 48,
                        color: Colors.white,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        description,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 14,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              ),
            ),
            actions: [
              IconButton(
                icon: const Icon(Icons.refresh, color: Colors.white),
                onPressed: _resetProgress,
              ),
            ],
          ),
          
          // Progress Summary
          SliverToBoxAdapter(
            child: Container(
              margin: const EdgeInsets.all(16),
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withOpacity(0.1),
                    blurRadius: 10,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: _buildProgressSummary(),
            ),
          ),
          
          // Dzikir Items List
          SliverList(
            delegate: SliverChildBuilderDelegate(
              (context, index) {
                // TODO: Replace with actual data from provider
                return _buildDzikirItem(index);
              },
              childCount: _getItemCount(), // TODO: Get from provider
            ),
          ),
          
          // Bottom padding
          const SliverToBoxAdapter(
            child: SizedBox(height: 100),
          ),
        ],
      ),
      
      // Floating Action Button
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _startDzikir,
        backgroundColor: _getThemeColor(),
        icon: const Icon(Icons.play_arrow, color: Colors.white),
        label: const Text(
          'Mulai Dzikir',
          style: TextStyle(color: Colors.white),
        ),
      ),
    );
  }

  Widget _buildProgressSummary() {
    // TODO: Get actual progress from provider
    const completed = 0;
    final total = _getItemCount();
    final progress = total > 0 ? completed / total : 0.0;
    
    return Column(
      children: [
        Row(
          children: [
            Icon(
              Icons.analytics,
              color: _getThemeColor(),
              size: 24,
            ),
            const SizedBox(width: 8),
            const Text(
              'Progress Dzikir',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: AppTheme.textPrimaryColor,
              ),
            ),
            const Spacer(),
            Text(
              AppUtils.getProgressText(completed, total),
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: _getThemeColor(),
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        LinearProgressIndicator(
          value: progress,
          backgroundColor: Colors.grey.withOpacity(0.2),
          valueColor: AlwaysStoppedAnimation<Color>(_getThemeColor()),
          minHeight: 8,
        ),
        const SizedBox(height: 12),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              '${AppUtils.getPercentageText(progress)} selesai',
              style: const TextStyle(
                fontSize: 14,
                color: AppTheme.textSecondaryColor,
              ),
            ),
            Text(
              '${total - completed} tersisa',
              style: const TextStyle(
                fontSize: 14,
                color: AppTheme.textSecondaryColor,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildDzikirItem(int index) {
    // TODO: Replace with actual DzikirItem data
    final sampleTitles = _getSampleTitles();
    final title = index < sampleTitles.length ? sampleTitles[index] : 'Dzikir ${index + 1}';
    const isCompleted = false; // TODO: Get from actual data
    const currentCount = 0; // TODO: Get from actual data
    const targetCount = 1; // TODO: Get from actual data
    
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.all(16),
        leading: Container(
          width: 48,
          height: 48,
          decoration: BoxDecoration(
            color: isCompleted 
                ? Colors.green.withOpacity(0.1)
                : _getThemeColor().withOpacity(0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Icon(
            isCompleted ? Icons.check_circle : Icons.book,
            color: isCompleted ? Colors.green : _getThemeColor(),
            size: 24,
          ),
        ),
        title: Text(
          title,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: AppTheme.textPrimaryColor,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 4),
            Text(
              'Target: $targetCount kali',
              style: const TextStyle(
                fontSize: 12,
                color: AppTheme.textSecondaryColor,
              ),
            ),
            const SizedBox(height: 8),
            LinearProgressIndicator(
              value: targetCount > 0 ? currentCount / targetCount : 0.0,
              backgroundColor: Colors.grey.withOpacity(0.2),
              valueColor: AlwaysStoppedAnimation<Color>(
                isCompleted ? Colors.green : _getThemeColor(),
              ),
              minHeight: 4,
            ),
          ],
        ),
        trailing: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CounterButton(
              count: currentCount,
              target: targetCount,
              isCompleted: isCompleted,
              onTap: () => _incrementCount(index),
            ),
          ],
        ),
        onTap: () => _navigateToDetail(index),
      ),
    );
  }

  Color _getThemeColor() {
    switch (widget.dzikirType) {
      case 'pagi':
        return Colors.orange;
      case 'petang':
        return Colors.indigo;
      case 'sesudah_sholat':
        return AppTheme.primaryColor;
      default:
        return AppTheme.primaryColor;
    }
  }

  IconData _getThemeIcon() {
    switch (widget.dzikirType) {
      case 'pagi':
        return Icons.wb_sunny;
      case 'petang':
        return Icons.nights_stay;
      case 'sesudah_sholat':
        return Icons.mosque;
      default:
        return Icons.book;
    }
  }

  int _getItemCount() {
    switch (widget.dzikirType) {
      case 'pagi':
      case 'petang':
        return 5;
      case 'sesudah_sholat':
        return 9;
      default:
        return 0;
    }
  }

  List<String> _getSampleTitles() {
    switch (widget.dzikirType) {
      case 'pagi':
      case 'petang':
        return [
          'Ayat Kursi',
          'Surat Al-Ikhlas',
          'Surat Al-Falaq',
          'Surat An-Naas',
          'Doa Pagi/Petang',
        ];
      case 'sesudah_sholat':
        return [
          'Astaghfirullah',
          'Doa Setelah Salam',
          'Ayat Kursi',
          'Surat Al-Ikhlas',
          'Surat Al-Falaq',
          'Surat An-Naas',
          'Tasbih',
          'Tahmid',
          'Takbir',
        ];
      default:
        return [];
    }
  }

  void _resetProgress() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Reset Progress'),
        content: const Text('Apakah Anda yakin ingin mereset progress dzikir ini?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Batal'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              // TODO: Reset progress via provider
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Progress berhasil direset')),
              );
            },
            child: const Text('Reset'),
          ),
        ],
      ),
    );
  }

  void _startDzikir() {
    // TODO: Navigate to first incomplete dzikir or show start dialog
    _navigateToDetail(0);
  }

  void _incrementCount(int index) {
    // TODO: Increment count via provider
    print('Increment count for item $index');
  }

  void _navigateToDetail(int index) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => DzikirDetailScreen(
          dzikirType: widget.dzikirType,
          itemIndex: index,
        ),
      ),
    );
  }
}
