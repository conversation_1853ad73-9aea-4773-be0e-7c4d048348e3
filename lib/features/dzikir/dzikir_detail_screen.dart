import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../core/theme/app_theme.dart';
import '../../core/utils/app_utils.dart';
import '../../shared/widgets/custom_button.dart';

class DzikirDetailScreen extends ConsumerStatefulWidget {
  final String dzikirType;
  final int itemIndex;

  const DzikirDetailScreen({
    super.key,
    required this.dzikirType,
    required this.itemIndex,
  });

  @override
  ConsumerState<DzikirDetailScreen> createState() => _DzikirDetailScreenState();
}

class _DzikirDetailScreenState extends ConsumerState<DzikirDetailScreen> {
  bool showTransliteration = true;
  bool showTranslation = true;
  
  // TODO: Replace with actual data from provider
  int currentCount = 0;
  int targetCount = 1;
  bool isCompleted = false;

  @override
  Widget build(BuildContext context) {
    final displayName = AppUtils.getDzikirDisplayName(widget.dzikirType);
    
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      body: CustomScrollView(
        slivers: [
          // App Bar
          SliverAppBar(
            expandedHeight: 120,
            floating: false,
            pinned: true,
            backgroundColor: _getThemeColor(),
            flexibleSpace: FlexibleSpaceBar(
              title: Text(
                _getDzikirTitle(),
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              ),
              background: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      _getThemeColor(),
                      _getThemeColor().withOpacity(0.8),
                    ],
                  ),
                ),
              ),
            ),
            actions: [
              IconButton(
                icon: const Icon(Icons.settings, color: Colors.white),
                onPressed: _showDisplaySettings,
              ),
            ],
          ),
          
          // Content
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  // Progress Card
                  _buildProgressCard(),
                  const SizedBox(height: 24),
                  
                  // Arabic Text
                  _buildArabicText(),
                  const SizedBox(height: 24),
                  
                  // Transliteration
                  if (showTransliteration) ...[
                    _buildTransliteration(),
                    const SizedBox(height: 24),
                  ],
                  
                  // Translation
                  if (showTranslation) ...[
                    _buildTranslation(),
                    const SizedBox(height: 24),
                  ],
                  
                  // Reference
                  _buildReference(),
                  const SizedBox(height: 32),
                  
                  // Counter Section
                  _buildCounterSection(),
                  const SizedBox(height: 100),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProgressCard() {
    final progress = targetCount > 0 ? currentCount / targetCount : 0.0;
    
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            children: [
              Icon(
                isCompleted ? Icons.check_circle : Icons.timer,
                color: isCompleted ? Colors.green : _getThemeColor(),
                size: 24,
              ),
              const SizedBox(width: 8),
              Text(
                isCompleted ? 'Selesai' : 'Progress',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: isCompleted ? Colors.green : _getThemeColor(),
                ),
              ),
              const Spacer(),
              Text(
                AppUtils.getProgressText(currentCount, targetCount),
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: _getThemeColor(),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          LinearProgressIndicator(
            value: progress,
            backgroundColor: Colors.grey.withOpacity(0.2),
            valueColor: AlwaysStoppedAnimation<Color>(
              isCompleted ? Colors.green : _getThemeColor(),
            ),
            minHeight: 8,
          ),
          const SizedBox(height: 8),
          Text(
            isCompleted 
                ? 'Alhamdulillah, dzikir ini telah selesai'
                : 'Tap tombol counter untuk melanjutkan',
            style: const TextStyle(
              fontSize: 12,
              color: AppTheme.textSecondaryColor,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildArabicText() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: _getThemeColor().withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Text(
        _getArabicText(),
        style: const TextStyle(
          fontSize: 28,
          height: 2.0,
          color: AppTheme.arabicTextColor,
          fontWeight: FontWeight.w500,
        ),
        textAlign: TextAlign.right,
        textDirection: TextDirection.rtl,
      ),
    );
  }

  Widget _buildTransliteration() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppTheme.primaryColor.withOpacity(0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppTheme.primaryColor.withOpacity(0.1),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.record_voice_over,
                size: 16,
                color: _getThemeColor(),
              ),
              const SizedBox(width: 8),
              Text(
                'Transliterasi',
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                  color: _getThemeColor(),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            _getTransliteration(),
            style: const TextStyle(
              fontSize: 16,
              height: 1.6,
              color: AppTheme.transliterationTextColor,
              fontStyle: FontStyle.italic,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTranslation() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.blue.withOpacity(0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.blue.withOpacity(0.1),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Row(
            children: [
              Icon(
                Icons.translate,
                size: 16,
                color: Colors.blue,
              ),
              SizedBox(width: 8),
              Text(
                'Terjemahan',
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                  color: Colors.blue,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            _getTranslation(),
            style: const TextStyle(
              fontSize: 14,
              height: 1.5,
              color: AppTheme.translationTextColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildReference() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.grey.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Text(
        'Rujukan: ${_getReference()}',
        style: const TextStyle(
          fontSize: 12,
          color: AppTheme.textSecondaryColor,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Widget _buildCounterSection() {
    return Column(
      children: [
        const Text(
          'Tap untuk menghitung',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: AppTheme.textPrimaryColor,
          ),
        ),
        const SizedBox(height: 24),
        
        // Large Counter Button
        GestureDetector(
          onTap: isCompleted ? null : _incrementCounter,
          child: Container(
            width: 120,
            height: 120,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: isCompleted ? Colors.green : _getThemeColor(),
              boxShadow: [
                BoxShadow(
                  color: (isCompleted ? Colors.green : _getThemeColor())
                      .withOpacity(0.3),
                  blurRadius: 20,
                  offset: const Offset(0, 8),
                ),
              ],
            ),
            child: Stack(
              alignment: Alignment.center,
              children: [
                // Progress circle
                SizedBox(
                  width: 100,
                  height: 100,
                  child: CircularProgressIndicator(
                    value: targetCount > 0 ? currentCount / targetCount : 0.0,
                    backgroundColor: Colors.white.withOpacity(0.3),
                    valueColor: const AlwaysStoppedAnimation<Color>(Colors.white),
                    strokeWidth: 4,
                  ),
                ),
                // Count text
                Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    if (isCompleted)
                      const Icon(
                        Icons.check,
                        color: Colors.white,
                        size: 32,
                      )
                    else ...[
                      Text(
                        '$currentCount',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 32,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        '/$targetCount',
                        style: TextStyle(
                          color: Colors.white.withOpacity(0.8),
                          fontSize: 16,
                        ),
                      ),
                    ],
                  ],
                ),
              ],
            ),
          ),
        ),
        
        const SizedBox(height: 24),
        
        // Action Buttons
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            CustomButton(
              text: 'Reset',
              type: ButtonType.outline,
              onPressed: _resetCounter,
              icon: Icons.refresh,
            ),
            CustomButton(
              text: isCompleted ? 'Selesai' : 'Tandai Selesai',
              type: ButtonType.primary,
              onPressed: isCompleted ? null : _markCompleted,
              icon: isCompleted ? Icons.check : Icons.done_all,
              color: isCompleted ? Colors.green : _getThemeColor(),
            ),
          ],
        ),
      ],
    );
  }

  Color _getThemeColor() {
    switch (widget.dzikirType) {
      case 'pagi':
        return Colors.orange;
      case 'petang':
        return Colors.indigo;
      case 'sesudah_sholat':
        return AppTheme.primaryColor;
      default:
        return AppTheme.primaryColor;
    }
  }

  String _getDzikirTitle() {
    // TODO: Get from actual data
    final sampleTitles = [
      'Ayat Kursi',
      'Surat Al-Ikhlas',
      'Surat Al-Falaq',
      'Surat An-Naas',
      'Doa Pagi/Petang',
    ];
    
    return widget.itemIndex < sampleTitles.length 
        ? sampleTitles[widget.itemIndex] 
        : 'Dzikir ${widget.itemIndex + 1}';
  }

  String _getArabicText() {
    // TODO: Get from actual data
    return 'اللَّهُ لَا إِلَٰهَ إِلَّا هُوَ الْحَيُّ الْقَيُّومُ';
  }

  String _getTransliteration() {
    // TODO: Get from actual data
    return 'Allahu la ilaha illa huwal hayyul qayyum';
  }

  String _getTranslation() {
    // TODO: Get from actual data
    return 'Allah, tidak ada Tuhan selain Dia, Yang Maha Hidup lagi terus menerus mengurus (makhluk-Nya).';
  }

  String _getReference() {
    // TODO: Get from actual data
    return 'QS. Al-Baqarah: 255';
  }

  void _incrementCounter() {
    if (!isCompleted && currentCount < targetCount) {
      setState(() {
        currentCount++;
        if (currentCount >= targetCount) {
          isCompleted = true;
        }
      });
      
      // TODO: Update via provider
      
      // Haptic feedback
      // HapticFeedback.lightImpact();
    }
  }

  void _resetCounter() {
    setState(() {
      currentCount = 0;
      isCompleted = false;
    });
    
    // TODO: Update via provider
  }

  void _markCompleted() {
    setState(() {
      currentCount = targetCount;
      isCompleted = true;
    });
    
    // TODO: Update via provider
  }

  void _showDisplaySettings() {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'Pengaturan Tampilan',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 24),
            SwitchListTile(
              title: const Text('Tampilkan Transliterasi'),
              value: showTransliteration,
              onChanged: (value) {
                setState(() {
                  showTransliteration = value;
                });
                Navigator.pop(context);
              },
            ),
            SwitchListTile(
              title: const Text('Tampilkan Terjemahan'),
              value: showTranslation,
              onChanged: (value) {
                setState(() {
                  showTranslation = value;
                });
                Navigator.pop(context);
              },
            ),
          ],
        ),
      ),
    );
  }
}
