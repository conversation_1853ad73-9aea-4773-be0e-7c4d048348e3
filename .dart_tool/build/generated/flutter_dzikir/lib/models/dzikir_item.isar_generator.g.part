// **************************************************************************
// IsarCollectionGenerator
// **************************************************************************

// coverage:ignore-file
// ignore_for_file: duplicate_ignore, non_constant_identifier_names, constant_identifier_names, invalid_use_of_protected_member, unnecessary_cast, prefer_const_constructors, lines_longer_than_80_chars, require_trailing_commas, inference_failure_on_function_invocation, unnecessary_parenthesis, unnecessary_raw_strings, unnecessary_null_checks, join_return_with_assignment, prefer_final_locals, avoid_js_rounded_ints, avoid_positional_boolean_parameters, always_specify_types

extension GetDzikirItemCollection on Isar {
  IsarCollection<DzikirItem> get dzikirItems => this.collection();
}

const DzikirItemSchema = CollectionSchema(
  name: r'DzikirItem',
  id: -7376348073210835954,
  properties: {
    r'arab': PropertySchema(
      id: 0,
      name: r'arab',
      type: IsarType.string,
    ),
    r'currentCount': PropertySchema(
      id: 1,
      name: r'currentCount',
      type: IsarType.long,
    ),
    r'isCompleted': PropertySchema(
      id: 2,
      name: r'isCompleted',
      type: IsarType.bool,
    ),
    r'isTargetReached': PropertySchema(
      id: 3,
      name: r'isTargetReached',
      type: IsarType.bool,
    ),
    r'itemId': PropertySchema(
      id: 4,
      name: r'itemId',
      type: IsarType.string,
    ),
    r'judul': PropertySchema(
      id: 5,
      name: r'judul',
      type: IsarType.string,
    ),
    r'jumlahTarget': PropertySchema(
      id: 6,
      name: r'jumlahTarget',
      type: IsarType.long,
    ),
    r'lastRead': PropertySchema(
      id: 7,
      name: r'lastRead',
      type: IsarType.dateTime,
    ),
    r'progressPercentage': PropertySchema(
      id: 8,
      name: r'progressPercentage',
      type: IsarType.double,
    ),
    r'rujukan': PropertySchema(
      id: 9,
      name: r'rujukan',
      type: IsarType.string,
    ),
    r'terjemahan': PropertySchema(
      id: 10,
      name: r'terjemahan',
      type: IsarType.string,
    ),
    r'transliterasi': PropertySchema(
      id: 11,
      name: r'transliterasi',
      type: IsarType.string,
    ),
    r'urutan': PropertySchema(
      id: 12,
      name: r'urutan',
      type: IsarType.long,
    ),
    r'waktu': PropertySchema(
      id: 13,
      name: r'waktu',
      type: IsarType.string,
    )
  },
  estimateSize: _dzikirItemEstimateSize,
  serialize: _dzikirItemSerialize,
  deserialize: _dzikirItemDeserialize,
  deserializeProp: _dzikirItemDeserializeProp,
  idName: r'id',
  indexes: {
    r'itemId': IndexSchema(
      id: -5342806140158601489,
      name: r'itemId',
      unique: false,
      replace: false,
      properties: [
        IndexPropertySchema(
          name: r'itemId',
          type: IndexType.hash,
          caseSensitive: true,
        )
      ],
    ),
    r'waktu': IndexSchema(
      id: -4204409312096578441,
      name: r'waktu',
      unique: false,
      replace: false,
      properties: [
        IndexPropertySchema(
          name: r'waktu',
          type: IndexType.hash,
          caseSensitive: true,
        )
      ],
    )
  },
  links: {},
  embeddedSchemas: {},
  getId: _dzikirItemGetId,
  getLinks: _dzikirItemGetLinks,
  attach: _dzikirItemAttach,
  version: '3.1.0+1',
);

int _dzikirItemEstimateSize(
  DzikirItem object,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  var bytesCount = offsets.last;
  bytesCount += 3 + object.arab.length * 3;
  bytesCount += 3 + object.itemId.length * 3;
  bytesCount += 3 + object.judul.length * 3;
  bytesCount += 3 + object.rujukan.length * 3;
  bytesCount += 3 + object.terjemahan.length * 3;
  bytesCount += 3 + object.transliterasi.length * 3;
  bytesCount += 3 + object.waktu.length * 3;
  return bytesCount;
}

void _dzikirItemSerialize(
  DzikirItem object,
  IsarWriter writer,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  writer.writeString(offsets[0], object.arab);
  writer.writeLong(offsets[1], object.currentCount);
  writer.writeBool(offsets[2], object.isCompleted);
  writer.writeBool(offsets[3], object.isTargetReached);
  writer.writeString(offsets[4], object.itemId);
  writer.writeString(offsets[5], object.judul);
  writer.writeLong(offsets[6], object.jumlahTarget);
  writer.writeDateTime(offsets[7], object.lastRead);
  writer.writeDouble(offsets[8], object.progressPercentage);
  writer.writeString(offsets[9], object.rujukan);
  writer.writeString(offsets[10], object.terjemahan);
  writer.writeString(offsets[11], object.transliterasi);
  writer.writeLong(offsets[12], object.urutan);
  writer.writeString(offsets[13], object.waktu);
}

DzikirItem _dzikirItemDeserialize(
  Id id,
  IsarReader reader,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  final object = DzikirItem();
  object.arab = reader.readString(offsets[0]);
  object.currentCount = reader.readLong(offsets[1]);
  object.id = id;
  object.isCompleted = reader.readBool(offsets[2]);
  object.itemId = reader.readString(offsets[4]);
  object.judul = reader.readString(offsets[5]);
  object.jumlahTarget = reader.readLong(offsets[6]);
  object.lastRead = reader.readDateTimeOrNull(offsets[7]);
  object.rujukan = reader.readString(offsets[9]);
  object.terjemahan = reader.readString(offsets[10]);
  object.transliterasi = reader.readString(offsets[11]);
  object.urutan = reader.readLong(offsets[12]);
  object.waktu = reader.readString(offsets[13]);
  return object;
}

P _dzikirItemDeserializeProp<P>(
  IsarReader reader,
  int propertyId,
  int offset,
  Map<Type, List<int>> allOffsets,
) {
  switch (propertyId) {
    case 0:
      return (reader.readString(offset)) as P;
    case 1:
      return (reader.readLong(offset)) as P;
    case 2:
      return (reader.readBool(offset)) as P;
    case 3:
      return (reader.readBool(offset)) as P;
    case 4:
      return (reader.readString(offset)) as P;
    case 5:
      return (reader.readString(offset)) as P;
    case 6:
      return (reader.readLong(offset)) as P;
    case 7:
      return (reader.readDateTimeOrNull(offset)) as P;
    case 8:
      return (reader.readDouble(offset)) as P;
    case 9:
      return (reader.readString(offset)) as P;
    case 10:
      return (reader.readString(offset)) as P;
    case 11:
      return (reader.readString(offset)) as P;
    case 12:
      return (reader.readLong(offset)) as P;
    case 13:
      return (reader.readString(offset)) as P;
    default:
      throw IsarError('Unknown property with id $propertyId');
  }
}

Id _dzikirItemGetId(DzikirItem object) {
  return object.id;
}

List<IsarLinkBase<dynamic>> _dzikirItemGetLinks(DzikirItem object) {
  return [];
}

void _dzikirItemAttach(IsarCollection<dynamic> col, Id id, DzikirItem object) {
  object.id = id;
}

extension DzikirItemQueryWhereSort
    on QueryBuilder<DzikirItem, DzikirItem, QWhere> {
  QueryBuilder<DzikirItem, DzikirItem, QAfterWhere> anyId() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(const IdWhereClause.any());
    });
  }
}

extension DzikirItemQueryWhere
    on QueryBuilder<DzikirItem, DzikirItem, QWhereClause> {
  QueryBuilder<DzikirItem, DzikirItem, QAfterWhereClause> idEqualTo(Id id) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: id,
        upper: id,
      ));
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterWhereClause> idNotEqualTo(Id id) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(
              IdWhereClause.lessThan(upper: id, includeUpper: false),
            )
            .addWhereClause(
              IdWhereClause.greaterThan(lower: id, includeLower: false),
            );
      } else {
        return query
            .addWhereClause(
              IdWhereClause.greaterThan(lower: id, includeLower: false),
            )
            .addWhereClause(
              IdWhereClause.lessThan(upper: id, includeUpper: false),
            );
      }
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterWhereClause> idGreaterThan(Id id,
      {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.greaterThan(lower: id, includeLower: include),
      );
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterWhereClause> idLessThan(Id id,
      {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.lessThan(upper: id, includeUpper: include),
      );
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterWhereClause> idBetween(
    Id lowerId,
    Id upperId, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: lowerId,
        includeLower: includeLower,
        upper: upperId,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterWhereClause> itemIdEqualTo(
      String itemId) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'itemId',
        value: [itemId],
      ));
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterWhereClause> itemIdNotEqualTo(
      String itemId) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'itemId',
              lower: [],
              upper: [itemId],
              includeUpper: false,
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'itemId',
              lower: [itemId],
              includeLower: false,
              upper: [],
            ));
      } else {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'itemId',
              lower: [itemId],
              includeLower: false,
              upper: [],
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'itemId',
              lower: [],
              upper: [itemId],
              includeUpper: false,
            ));
      }
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterWhereClause> waktuEqualTo(
      String waktu) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'waktu',
        value: [waktu],
      ));
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterWhereClause> waktuNotEqualTo(
      String waktu) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'waktu',
              lower: [],
              upper: [waktu],
              includeUpper: false,
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'waktu',
              lower: [waktu],
              includeLower: false,
              upper: [],
            ));
      } else {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'waktu',
              lower: [waktu],
              includeLower: false,
              upper: [],
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'waktu',
              lower: [],
              upper: [waktu],
              includeUpper: false,
            ));
      }
    });
  }
}

extension DzikirItemQueryFilter
    on QueryBuilder<DzikirItem, DzikirItem, QFilterCondition> {
  QueryBuilder<DzikirItem, DzikirItem, QAfterFilterCondition> arabEqualTo(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'arab',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterFilterCondition> arabGreaterThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'arab',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterFilterCondition> arabLessThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'arab',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterFilterCondition> arabBetween(
    String lower,
    String upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'arab',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterFilterCondition> arabStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'arab',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterFilterCondition> arabEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'arab',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterFilterCondition> arabContains(
      String value,
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'arab',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterFilterCondition> arabMatches(
      String pattern,
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'arab',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterFilterCondition> arabIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'arab',
        value: '',
      ));
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterFilterCondition> arabIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'arab',
        value: '',
      ));
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterFilterCondition>
      currentCountEqualTo(int value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'currentCount',
        value: value,
      ));
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterFilterCondition>
      currentCountGreaterThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'currentCount',
        value: value,
      ));
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterFilterCondition>
      currentCountLessThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'currentCount',
        value: value,
      ));
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterFilterCondition>
      currentCountBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'currentCount',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterFilterCondition> idEqualTo(
      Id value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterFilterCondition> idGreaterThan(
    Id value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterFilterCondition> idLessThan(
    Id value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterFilterCondition> idBetween(
    Id lower,
    Id upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'id',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterFilterCondition>
      isCompletedEqualTo(bool value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'isCompleted',
        value: value,
      ));
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterFilterCondition>
      isTargetReachedEqualTo(bool value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'isTargetReached',
        value: value,
      ));
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterFilterCondition> itemIdEqualTo(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'itemId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterFilterCondition> itemIdGreaterThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'itemId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterFilterCondition> itemIdLessThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'itemId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterFilterCondition> itemIdBetween(
    String lower,
    String upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'itemId',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterFilterCondition> itemIdStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'itemId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterFilterCondition> itemIdEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'itemId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterFilterCondition> itemIdContains(
      String value,
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'itemId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterFilterCondition> itemIdMatches(
      String pattern,
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'itemId',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterFilterCondition> itemIdIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'itemId',
        value: '',
      ));
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterFilterCondition>
      itemIdIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'itemId',
        value: '',
      ));
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterFilterCondition> judulEqualTo(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'judul',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterFilterCondition> judulGreaterThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'judul',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterFilterCondition> judulLessThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'judul',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterFilterCondition> judulBetween(
    String lower,
    String upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'judul',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterFilterCondition> judulStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'judul',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterFilterCondition> judulEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'judul',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterFilterCondition> judulContains(
      String value,
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'judul',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterFilterCondition> judulMatches(
      String pattern,
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'judul',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterFilterCondition> judulIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'judul',
        value: '',
      ));
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterFilterCondition>
      judulIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'judul',
        value: '',
      ));
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterFilterCondition>
      jumlahTargetEqualTo(int value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'jumlahTarget',
        value: value,
      ));
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterFilterCondition>
      jumlahTargetGreaterThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'jumlahTarget',
        value: value,
      ));
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterFilterCondition>
      jumlahTargetLessThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'jumlahTarget',
        value: value,
      ));
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterFilterCondition>
      jumlahTargetBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'jumlahTarget',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterFilterCondition> lastReadIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'lastRead',
      ));
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterFilterCondition>
      lastReadIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'lastRead',
      ));
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterFilterCondition> lastReadEqualTo(
      DateTime? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'lastRead',
        value: value,
      ));
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterFilterCondition>
      lastReadGreaterThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'lastRead',
        value: value,
      ));
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterFilterCondition> lastReadLessThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'lastRead',
        value: value,
      ));
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterFilterCondition> lastReadBetween(
    DateTime? lower,
    DateTime? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'lastRead',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterFilterCondition>
      progressPercentageEqualTo(
    double value, {
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'progressPercentage',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterFilterCondition>
      progressPercentageGreaterThan(
    double value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'progressPercentage',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterFilterCondition>
      progressPercentageLessThan(
    double value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'progressPercentage',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterFilterCondition>
      progressPercentageBetween(
    double lower,
    double upper, {
    bool includeLower = true,
    bool includeUpper = true,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'progressPercentage',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterFilterCondition> rujukanEqualTo(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'rujukan',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterFilterCondition>
      rujukanGreaterThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'rujukan',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterFilterCondition> rujukanLessThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'rujukan',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterFilterCondition> rujukanBetween(
    String lower,
    String upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'rujukan',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterFilterCondition> rujukanStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'rujukan',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterFilterCondition> rujukanEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'rujukan',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterFilterCondition> rujukanContains(
      String value,
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'rujukan',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterFilterCondition> rujukanMatches(
      String pattern,
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'rujukan',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterFilterCondition> rujukanIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'rujukan',
        value: '',
      ));
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterFilterCondition>
      rujukanIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'rujukan',
        value: '',
      ));
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterFilterCondition> terjemahanEqualTo(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'terjemahan',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterFilterCondition>
      terjemahanGreaterThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'terjemahan',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterFilterCondition>
      terjemahanLessThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'terjemahan',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterFilterCondition> terjemahanBetween(
    String lower,
    String upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'terjemahan',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterFilterCondition>
      terjemahanStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'terjemahan',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterFilterCondition>
      terjemahanEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'terjemahan',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterFilterCondition>
      terjemahanContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'terjemahan',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterFilterCondition> terjemahanMatches(
      String pattern,
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'terjemahan',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterFilterCondition>
      terjemahanIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'terjemahan',
        value: '',
      ));
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterFilterCondition>
      terjemahanIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'terjemahan',
        value: '',
      ));
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterFilterCondition>
      transliterasiEqualTo(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'transliterasi',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterFilterCondition>
      transliterasiGreaterThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'transliterasi',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterFilterCondition>
      transliterasiLessThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'transliterasi',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterFilterCondition>
      transliterasiBetween(
    String lower,
    String upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'transliterasi',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterFilterCondition>
      transliterasiStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'transliterasi',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterFilterCondition>
      transliterasiEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'transliterasi',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterFilterCondition>
      transliterasiContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'transliterasi',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterFilterCondition>
      transliterasiMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'transliterasi',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterFilterCondition>
      transliterasiIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'transliterasi',
        value: '',
      ));
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterFilterCondition>
      transliterasiIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'transliterasi',
        value: '',
      ));
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterFilterCondition> urutanEqualTo(
      int value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'urutan',
        value: value,
      ));
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterFilterCondition> urutanGreaterThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'urutan',
        value: value,
      ));
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterFilterCondition> urutanLessThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'urutan',
        value: value,
      ));
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterFilterCondition> urutanBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'urutan',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterFilterCondition> waktuEqualTo(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'waktu',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterFilterCondition> waktuGreaterThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'waktu',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterFilterCondition> waktuLessThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'waktu',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterFilterCondition> waktuBetween(
    String lower,
    String upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'waktu',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterFilterCondition> waktuStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'waktu',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterFilterCondition> waktuEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'waktu',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterFilterCondition> waktuContains(
      String value,
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'waktu',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterFilterCondition> waktuMatches(
      String pattern,
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'waktu',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterFilterCondition> waktuIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'waktu',
        value: '',
      ));
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterFilterCondition>
      waktuIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'waktu',
        value: '',
      ));
    });
  }
}

extension DzikirItemQueryObject
    on QueryBuilder<DzikirItem, DzikirItem, QFilterCondition> {}

extension DzikirItemQueryLinks
    on QueryBuilder<DzikirItem, DzikirItem, QFilterCondition> {}

extension DzikirItemQuerySortBy
    on QueryBuilder<DzikirItem, DzikirItem, QSortBy> {
  QueryBuilder<DzikirItem, DzikirItem, QAfterSortBy> sortByArab() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'arab', Sort.asc);
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterSortBy> sortByArabDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'arab', Sort.desc);
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterSortBy> sortByCurrentCount() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'currentCount', Sort.asc);
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterSortBy> sortByCurrentCountDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'currentCount', Sort.desc);
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterSortBy> sortByIsCompleted() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isCompleted', Sort.asc);
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterSortBy> sortByIsCompletedDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isCompleted', Sort.desc);
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterSortBy> sortByIsTargetReached() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isTargetReached', Sort.asc);
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterSortBy>
      sortByIsTargetReachedDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isTargetReached', Sort.desc);
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterSortBy> sortByItemId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'itemId', Sort.asc);
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterSortBy> sortByItemIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'itemId', Sort.desc);
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterSortBy> sortByJudul() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'judul', Sort.asc);
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterSortBy> sortByJudulDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'judul', Sort.desc);
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterSortBy> sortByJumlahTarget() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'jumlahTarget', Sort.asc);
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterSortBy> sortByJumlahTargetDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'jumlahTarget', Sort.desc);
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterSortBy> sortByLastRead() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'lastRead', Sort.asc);
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterSortBy> sortByLastReadDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'lastRead', Sort.desc);
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterSortBy>
      sortByProgressPercentage() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'progressPercentage', Sort.asc);
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterSortBy>
      sortByProgressPercentageDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'progressPercentage', Sort.desc);
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterSortBy> sortByRujukan() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'rujukan', Sort.asc);
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterSortBy> sortByRujukanDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'rujukan', Sort.desc);
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterSortBy> sortByTerjemahan() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'terjemahan', Sort.asc);
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterSortBy> sortByTerjemahanDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'terjemahan', Sort.desc);
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterSortBy> sortByTransliterasi() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'transliterasi', Sort.asc);
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterSortBy> sortByTransliterasiDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'transliterasi', Sort.desc);
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterSortBy> sortByUrutan() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'urutan', Sort.asc);
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterSortBy> sortByUrutanDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'urutan', Sort.desc);
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterSortBy> sortByWaktu() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'waktu', Sort.asc);
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterSortBy> sortByWaktuDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'waktu', Sort.desc);
    });
  }
}

extension DzikirItemQuerySortThenBy
    on QueryBuilder<DzikirItem, DzikirItem, QSortThenBy> {
  QueryBuilder<DzikirItem, DzikirItem, QAfterSortBy> thenByArab() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'arab', Sort.asc);
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterSortBy> thenByArabDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'arab', Sort.desc);
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterSortBy> thenByCurrentCount() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'currentCount', Sort.asc);
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterSortBy> thenByCurrentCountDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'currentCount', Sort.desc);
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterSortBy> thenById() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.asc);
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterSortBy> thenByIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.desc);
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterSortBy> thenByIsCompleted() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isCompleted', Sort.asc);
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterSortBy> thenByIsCompletedDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isCompleted', Sort.desc);
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterSortBy> thenByIsTargetReached() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isTargetReached', Sort.asc);
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterSortBy>
      thenByIsTargetReachedDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isTargetReached', Sort.desc);
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterSortBy> thenByItemId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'itemId', Sort.asc);
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterSortBy> thenByItemIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'itemId', Sort.desc);
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterSortBy> thenByJudul() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'judul', Sort.asc);
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterSortBy> thenByJudulDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'judul', Sort.desc);
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterSortBy> thenByJumlahTarget() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'jumlahTarget', Sort.asc);
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterSortBy> thenByJumlahTargetDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'jumlahTarget', Sort.desc);
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterSortBy> thenByLastRead() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'lastRead', Sort.asc);
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterSortBy> thenByLastReadDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'lastRead', Sort.desc);
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterSortBy>
      thenByProgressPercentage() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'progressPercentage', Sort.asc);
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterSortBy>
      thenByProgressPercentageDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'progressPercentage', Sort.desc);
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterSortBy> thenByRujukan() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'rujukan', Sort.asc);
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterSortBy> thenByRujukanDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'rujukan', Sort.desc);
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterSortBy> thenByTerjemahan() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'terjemahan', Sort.asc);
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterSortBy> thenByTerjemahanDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'terjemahan', Sort.desc);
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterSortBy> thenByTransliterasi() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'transliterasi', Sort.asc);
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterSortBy> thenByTransliterasiDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'transliterasi', Sort.desc);
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterSortBy> thenByUrutan() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'urutan', Sort.asc);
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterSortBy> thenByUrutanDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'urutan', Sort.desc);
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterSortBy> thenByWaktu() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'waktu', Sort.asc);
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QAfterSortBy> thenByWaktuDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'waktu', Sort.desc);
    });
  }
}

extension DzikirItemQueryWhereDistinct
    on QueryBuilder<DzikirItem, DzikirItem, QDistinct> {
  QueryBuilder<DzikirItem, DzikirItem, QDistinct> distinctByArab(
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'arab', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QDistinct> distinctByCurrentCount() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'currentCount');
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QDistinct> distinctByIsCompleted() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'isCompleted');
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QDistinct> distinctByIsTargetReached() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'isTargetReached');
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QDistinct> distinctByItemId(
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'itemId', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QDistinct> distinctByJudul(
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'judul', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QDistinct> distinctByJumlahTarget() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'jumlahTarget');
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QDistinct> distinctByLastRead() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'lastRead');
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QDistinct>
      distinctByProgressPercentage() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'progressPercentage');
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QDistinct> distinctByRujukan(
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'rujukan', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QDistinct> distinctByTerjemahan(
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'terjemahan', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QDistinct> distinctByTransliterasi(
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'transliterasi',
          caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QDistinct> distinctByUrutan() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'urutan');
    });
  }

  QueryBuilder<DzikirItem, DzikirItem, QDistinct> distinctByWaktu(
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'waktu', caseSensitive: caseSensitive);
    });
  }
}

extension DzikirItemQueryProperty
    on QueryBuilder<DzikirItem, DzikirItem, QQueryProperty> {
  QueryBuilder<DzikirItem, int, QQueryOperations> idProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'id');
    });
  }

  QueryBuilder<DzikirItem, String, QQueryOperations> arabProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'arab');
    });
  }

  QueryBuilder<DzikirItem, int, QQueryOperations> currentCountProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'currentCount');
    });
  }

  QueryBuilder<DzikirItem, bool, QQueryOperations> isCompletedProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'isCompleted');
    });
  }

  QueryBuilder<DzikirItem, bool, QQueryOperations> isTargetReachedProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'isTargetReached');
    });
  }

  QueryBuilder<DzikirItem, String, QQueryOperations> itemIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'itemId');
    });
  }

  QueryBuilder<DzikirItem, String, QQueryOperations> judulProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'judul');
    });
  }

  QueryBuilder<DzikirItem, int, QQueryOperations> jumlahTargetProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'jumlahTarget');
    });
  }

  QueryBuilder<DzikirItem, DateTime?, QQueryOperations> lastReadProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'lastRead');
    });
  }

  QueryBuilder<DzikirItem, double, QQueryOperations>
      progressPercentageProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'progressPercentage');
    });
  }

  QueryBuilder<DzikirItem, String, QQueryOperations> rujukanProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'rujukan');
    });
  }

  QueryBuilder<DzikirItem, String, QQueryOperations> terjemahanProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'terjemahan');
    });
  }

  QueryBuilder<DzikirItem, String, QQueryOperations> transliterasiProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'transliterasi');
    });
  }

  QueryBuilder<DzikirItem, int, QQueryOperations> urutanProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'urutan');
    });
  }

  QueryBuilder<DzikirItem, String, QQueryOperations> waktuProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'waktu');
    });
  }
}
