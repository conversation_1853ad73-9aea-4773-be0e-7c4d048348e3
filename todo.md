# Buatkan aplikasi mobile “<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, dan <PERSON>” berbasis Flutter dengan dukungan offline penuh

Instruksi:
<PERSON><PERSON> ingin <PERSON>a merancang aplikasi Flutter dengan spesifikasi berikut:

⸻

1. <PERSON><PERSON><PERSON> aplik<PERSON>
	•	Menyediakan bacaan dzikir pagi, d<PERSON><PERSON><PERSON> petang, dan dzikir sesudah sholat sesuai sunnah.
	•	<PERSON><PERSON><PERSON> konten dapat digunakan secara offline penuh.
	•	<PERSON><PERSON><PERSON><PERSON> teks <PERSON>, transliterasi, terjemahan bahasa Indonesia, dan jumlah bacaan.
	•	Ada pengingat otomatis untuk dzikir pagi dan petang.
	•	Ada penghitung dzikir untuk memudahkan pengguna.

⸻

2. Teknologi yang digunakan
	•	Flutter FVM 3.32.4
	•	State management: Riverpod atau Bloc.
	•	Database lokal: Isar.
	•	Notifikasi lokal: flutter_local_notifications + timezone.
	•	Background update: workmanager.
	•	Perhitungan waktu salat: adhan_dart.
	•	Lokasi: geolocator (opsional).
	•	Audio (opsional): just_audio.

⸻

3. Konten Dzikir

A<PERSON> (setelah <PERSON>uh hingga sebelum Zuhur)
	1.	Membaca Ayat Ku<PERSON> (QS. Al-Baqarah: 255) – 1x
	2.	Membaca Al-Ikhlas, Al-Falaq, An-Naas – masing-masing 3x
	3.	Membaca doa: “Aṣbaḥnā wa aṣbaḥal mulku lillāh…” (HR. Muslim)
	4.	Membaca doa: “Allāhumma bika aṣbaḥnā…” (HR. Abu Dawud, Tirmidzi)
	5.	Membaca doa: “Allāhumma inni as’aluka ‘ilman nāfi‘an…” (HR. Ibnu Majah)
	6.	Membaca doa: “Allāhumma ‘āfini fi badani…” (HR. Abu Dawud)
	7.	Membaca doa: “Radītu billāhi rabban…” (HR. Abu Dawud, Tirmidzi) – 3x
	8.	Membaca doa: “Allāhumma inni asbaḥtu usyhiduka…” (HR. Abu Dawud) – 4x
	9.	Membaca doa: “Hasbiyallāhu lā ilāha illā huwa…” (QS. At-Taubah: 129) – 7x
	10.	Membaca doa: “Bismillāhilladzi lā yadurru…” – 3x

B. Dzikir Petang (setelah Asar hingga sebelum Isya)

(Daftar sama dengan dzikir pagi, hanya redaksi doa disesuaikan dengan waktu malam, contoh: “Amsainā wa amsal mulku lillāh…”)

C. Dzikir Sesudah Sholat Fardhu
	1.	Membaca Astaghfirullāh – 3x
	2.	Membaca doa: “Allāhumma antas-salām wa minkas-salām…” (HR. Muslim)
	3.	Membaca Ayat Kursi – 1x
	4.	Membaca Al-Ikhlas, Al-Falaq, An-Naas – masing-masing 1x (HR. Abu Dawud)
	5.	Membaca Tasbih 33x, Tahmid 33x, Takbir 34x
(atau 33x tasbih, 33x tahmid, 33x takbir + “Lā ilāha illallāhu waḥdahu…” – HR. Muslim)
	6.	Doa tambahan: “Lā ilāha illallāhu waḥdahu lā syarīka lah…” (HR. Bukhari, Muslim)

⸻

4. Fitur MVP
	•	Katalog dzikir pagi, petang, sesudah sholat (bundled offline dalam JSON).
	•	Teks Arab dengan font besar, transliterasi, terjemahan Indonesia.
	•	Penghitung bacaan sesuai target (misalnya 3x, 33x).
	•	Notifikasi otomatis pagi & petang.
	•	Riwayat progres dan streak.
	•	Mode offline penuh.

⸻

5. Struktur Data JSON (contoh)

{
  "id": "pagi_ayat_kursi",
  "judul": "Ayat Kursi",
  "arab": "اللّهُ لا إله إلا هو الحي القيوم …",
  "transliterasi": "Allahu la ilaha illa huwa al-hayyul-qayyum …",
  "terjemahan": "Allah, tidak ada Tuhan selain Dia, Yang Maha Hidup …",
  "jumlahTarget": 1,
  "waktu": "pagi",
  "rujukan": "QS. Al-Baqarah: 255"
}


⸻

6. Desain UI
	•	Beranda: tiga kartu utama → Dzikir Pagi, Dzikir Petang, Dzikir Sesudah Sholat.
	•	Layar Bacaan: teks Arab besar, transliterasi/terjemahan toggle, tombol penghitung besar.
	•	Pengaturan: jam pengingat, ukuran font, tema, lokasi/waktu salat.

⸻

7. Alur Notifikasi
	•	Dzikir Pagi: 30 menit setelah Subuh.
	•	Dzikir Petang: 30 menit setelah Asar.
	•	Pengguna bisa atur jam manual.
	•	Saat ditekan notifikasi → langsung membuka daftar dzikir sesuai waktu.

⸻

8. Rencana kerja 8 minggu
	1.	Minggu 1–2: susun konten dzikir, desain UI.
	2.	Minggu 3–4: implementasi layar bacaan + penyimpanan lokal.
	3.	Minggu 5: implementasi notifikasi + logika waktu salat.
	4.	Minggu 6: tambah riwayat & streak.
	5.	Minggu 7: tambahkan audio opsional.
	6.	Minggu 8: stabilisasi, uji beta, rilis.

9. Monetisasi
	1.	Aplikasi gratis dengan semua rangkaian dzikir inti.
	2.	Paket berbayar untuk audio lengkap, tema khusus, sinkronisasi, dan mode keluarga.